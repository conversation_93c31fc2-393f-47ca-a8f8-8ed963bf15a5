# -*- coding: utf-8 -*-
"""
IPC树形结构构建器
从MySQL读取节点和关系数据，构建完整的树形结构并输出到Excel
"""

import pandas as pd
import pymysql
from sqlalchemy import create_engine
from typing import Dict, List, Set
import sys

# 数据库配置
DB_CONFIG = {
    'host': '*************',
    'port': 3306,
    'user': 'root',
    'password': '12345678',
    'database': 'ipc',
    'charset': 'utf8mb4'
}


class IPCTreeBuilder:
    def __init__(self, db_config: dict):
        self.db_config = db_config
        self.engine = None
        self.nodes_df = None
        self.relations_df = None
        self.tree_data = []

    def connect_db(self):
        """连接数据库"""
        try:
            connection_string = (
                f"mysql+pymysql://{self.db_config['user']}:{self.db_config['password']}"
                f"@{self.db_config['host']}:{self.db_config['port']}/{self.db_config['database']}"
                f"?charset={self.db_config['charset']}"
            )
            self.engine = create_engine(connection_string)
            print("✅ 数据库连接成功")
            return True
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False

    def load_data(self):
        """从数据库加载数据"""
        try:
            # 读取节点表
            node_sql = """
                       SELECT id, pnr, kwd, item_id_set, is_root, level
                       FROM ipc_pn_tree_node
                       WHERE data_status = 'Valid'
                       """
            self.nodes_df = pd.read_sql(node_sql, self.engine)
            print(f"✅ 加载节点数据: {len(self.nodes_df)} 条")

            # 读取关系表
            rel_sql = "SELECT n_id, p_id FROM ipc_pn_tree_rel"
            self.relations_df = pd.read_sql(rel_sql, self.engine)
            print(f"✅ 加载关系数据: {len(self.relations_df)} 条")

            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False

    def build_parent_child_map(self) -> Dict[int, List[int]]:
        """构建父子关系映射"""
        parent_child_map = {}
        for _, row in self.relations_df.iterrows():
            parent_id = row['p_id']
            child_id = row['n_id']

            if parent_id not in parent_child_map:
                parent_child_map[parent_id] = []
            parent_child_map[parent_id].append(child_id)

        return parent_child_map

    def get_node_info(self, node_id: int) -> Dict:
        """获取节点信息"""
        node_row = self.nodes_df[self.nodes_df['id'] == node_id]
        if node_row.empty:
            return None

        row = node_row.iloc[0]
        return {
            'id': row['id'],
            'pnr': row['pnr'] if pd.notna(row['pnr']) else '',
            'kwd': row['kwd'] if pd.notna(row['kwd']) else '',
            'item_id_set': row['item_id_set'] if pd.notna(row['item_id_set']) else '',
            'is_root': row['is_root'],
            'level': row['level'] if pd.notna(row['level']) else 0
        }

    def traverse_tree(self, node_id: int, branch_id: int, sequence: int,
                      parent_child_map: Dict[int, List[int]], visited: Set[int]) -> int:
        """递归遍历树形结构"""
        if node_id in visited:
            return sequence

        visited.add(node_id)

        # 获取当前节点信息
        node_info = self.get_node_info(node_id)
        if node_info is None:
            return sequence

        # 添加到结果列表
        self.tree_data.append({
            'id': node_info['id'],
            'pnr': node_info['pnr'],
            'kwd': node_info['kwd'],
            'item_id_set': node_info['item_id_set'],
            'branch_id': branch_id,
            'sequence': sequence
        })

        current_sequence = sequence

        # 递归处理子节点
        if node_id in parent_child_map:
            children = parent_child_map[node_id]
            # 按子节点ID排序，确保顺序一致
            children.sort()

            for child_id in children:
                current_sequence += 1
                current_sequence = self.traverse_tree(
                    child_id, branch_id, current_sequence,
                    parent_child_map, visited
                )

        return current_sequence

    def build_trees(self):
        """构建所有树形结构"""
        # 获取所有根节点
        root_nodes = self.nodes_df[self.nodes_df['is_root'] == 1]['id'].tolist()
        print(f"✅ 找到根节点: {len(root_nodes)} 个")

        if not root_nodes:
            print("❌ 未找到根节点")
            return False

        # 构建父子关系映射
        parent_child_map = self.build_parent_child_map()
        print(f"✅ 构建父子关系映射完成")

        # 遍历每个根节点，构建树枝
        visited_global = set()

        for branch_id, root_id in enumerate(root_nodes, 1):
            if root_id in visited_global:
                continue

            print(f"🌳 处理第 {branch_id} 个树枝，根节点ID: {root_id}")

            visited_branch = set()
            self.traverse_tree(root_id, branch_id, 1, parent_child_map, visited_branch)
            visited_global.update(visited_branch)

        print(f"✅ 树形结构构建完成，共生成 {len(self.tree_data)} 条记录")
        return True

    def export_to_excel(self, output_file: str = "ipc_tree_structure.xlsx"):
        """导出到Excel文件"""
        try:
            if not self.tree_data:
                print("❌ 没有数据可导出")
                return False

            # 转换为DataFrame
            df = pd.DataFrame(self.tree_data)
            print(f"📊 准备导出数据: {len(df)} 条记录")

            # 按branch_id和sequence排序
            df = df.sort_values(['branch_id', 'sequence']).reset_index(drop=True)

            # 检查数据量，如果超过100万条，分批导出
            if len(df) > 1000000:
                print("⚠️ 数据量较大，将分批导出...")
                return self._export_large_data(df, output_file)
            else:
                return self._export_normal_data(df, output_file)

        except Exception as e:
            print(f"❌ 导出Excel失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _export_normal_data(self, df: pd.DataFrame, output_file: str) -> bool:
        """正常数据量导出"""
        try:
            # 使用xlsxwriter引擎，更稳定
            with pd.ExcelWriter(output_file, engine='xlsxwriter') as writer:
                df.to_excel(writer, sheet_name='IPC树形结构', index=False)

                # 添加统计信息
                stats_data = {
                    '统计项': ['总记录数', '树枝数量', '平均每枝记录数'],
                    '数值': [
                        len(df),
                        df['branch_id'].nunique(),
                        round(len(df) / df['branch_id'].nunique(), 2)
                    ]
                }
                stats_df = pd.DataFrame(stats_data)
                stats_df.to_excel(writer, sheet_name='统计信息', index=False)

            self._print_export_summary(df, output_file)
            return True

        except Exception as e:
            print(f"❌ 正常导出失败，尝试使用openpyxl: {e}")
            try:
                # 备选方案：使用openpyxl
                with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                    df.to_excel(writer, sheet_name='IPC树形结构', index=False)

                    stats_data = {
                        '统计项': ['总记录数', '树枝数量', '平均每枝记录数'],
                        '数值': [
                            len(df),
                            df['branch_id'].nunique(),
                            round(len(df) / df['branch_id'].nunique(), 2)
                        ]
                    }
                    stats_df = pd.DataFrame(stats_data)
                    stats_df.to_excel(writer, sheet_name='统计信息', index=False)

                self._print_export_summary(df, output_file)
                return True
            except Exception as e2:
                print(f"❌ openpyxl也失败，导出为CSV: {e2}")
                return self.export_to_csv(output_file.replace('.xlsx', '.csv'))

    def _export_large_data(self, df: pd.DataFrame, output_file: str) -> bool:
        """大数据量分批导出"""
        try:
            # 分批大小
            batch_size = 500000
            total_batches = (len(df) + batch_size - 1) // batch_size

            print(f"📦 将分为 {total_batches} 个文件导出")

            # 分批导出
            for i in range(total_batches):
                start_idx = i * batch_size
                end_idx = min((i + 1) * batch_size, len(df))
                batch_df = df.iloc[start_idx:end_idx].copy()

                # 生成文件名
                if total_batches == 1:
                    batch_file = output_file
                else:
                    name_parts = output_file.rsplit('.', 1)
                    batch_file = f"{name_parts[0]}_part{i + 1}.{name_parts[1]}"

                print(f"📝 导出第 {i + 1}/{total_batches} 批: {len(batch_df)} 条记录 -> {batch_file}")

                # 导出当前批次
                with pd.ExcelWriter(batch_file, engine='xlsxwriter') as writer:
                    batch_df.to_excel(writer, sheet_name='IPC树形结构', index=False)

                    # 只在第一个文件中添加统计信息
                    if i == 0:
                        stats_data = {
                            '统计项': ['总记录数', '树枝数量', '平均每枝记录数', '分批文件数'],
                            '数值': [
                                len(df),
                                df['branch_id'].nunique(),
                                round(len(df) / df['branch_id'].nunique(), 2),
                                total_batches
                            ]
                        }
                        stats_df = pd.DataFrame(stats_data)
                        stats_df.to_excel(writer, sheet_name='统计信息', index=False)

            # 输出汇总信息
            print(f"\n✅ 大数据量导出完成!")
            print(f"   总记录数: {len(df)}")
            print(f"   树枝数量: {df['branch_id'].nunique()}")
            print(f"   分批文件数: {total_batches}")

            return True

        except Exception as e:
            print(f"❌ 分批导出失败: {e}")
            return False

    def export_to_csv(self, output_file: str = "ipc_tree_structure.csv"):
        """导出到CSV文件（备选方案）"""
        try:
            if not self.tree_data:
                print("❌ 没有数据可导出")
                return False

            df = pd.DataFrame(self.tree_data)
            df = df.sort_values(['branch_id', 'sequence']).reset_index(drop=True)

            # 导出CSV
            csv_file = output_file.replace('.xlsx', '.csv')
            df.to_csv(csv_file, index=False, encoding='utf-8-sig')

            print(f"✅ 数据已导出到CSV: {csv_file}")
            print(f"   总记录数: {len(df)}")
            print(f"   树枝数量: {df['branch_id'].nunique()}")

            return True

        except Exception as e:
            print(f"❌ 导出CSV失败: {e}")
            return False

    def _print_export_summary(self, df: pd.DataFrame, output_file: str):
        """打印导出汇总信息"""
        print(f"✅ 数据已导出到: {output_file}")
        print(f"   总记录数: {len(df)}")
        print(f"   树枝数量: {df['branch_id'].nunique()}")

        # 显示每个树枝的记录数（只显示前10个）
        branch_counts = df['branch_id'].value_counts().sort_index()
        print(f"\n各树枝记录数（前10个）:")
        for branch_id, count in branch_counts.head(10).items():
            print(f"   树枝 {branch_id}: {count} 条记录")

        if len(branch_counts) > 10:
            print(f"   ... 还有 {len(branch_counts) - 10} 个树枝")

    def run(self, output_file: str = "ipc_tree_structure.xlsx"):
        """执行完整流程"""
        print("=== IPC树形结构构建器 ===")

        # 1. 连接数据库
        if not self.connect_db():
            return False

        # 2. 加载数据
        if not self.load_data():
            return False

        # 3. 构建树形结构
        if not self.build_trees():
            return False

        # 4. 导出Excel
        if not self.export_to_excel(output_file):
            return False

        print("🎉 任务完成!")
        return True


def main():
    """主函数"""
    # 使用正确的数据库配置
    db_config = {
        'host': '*************',
        'port': 3306,
        'user': 'root',
        'password': '12345678',
        'database': 'ipc',
        'charset': 'utf8mb4'
    }

    # 创建构建器实例
    builder = IPCTreeBuilder(db_config)

    # 执行构建任务
    success = builder.run("ipc_tree_structure.xlsx")

    if not success:
        sys.exit(1)


if __name__ == "__main__":
    main()