#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IPC树枝分析工具
功能：为780个件号找到最优的IPC树枝
"""

import pandas as pd
import numpy as np
from collections import defaultdict, Counter

def read_excel_files():
    """读取两个Excel文件"""
    try:
        # 读取表1：780个件号清单
        df1_path = "data/780个需重新梳理树结构737ng器材清单20250822v1.xlsx"
        df1 = pd.read_excel(df1_path)
        print(f"表1读取成功，共{len(df1)}行数据")
        print(f"表1列名：{list(df1.columns)}")
        
        # 读取表2：IPC上下级关系
        df2_path = "data/ipc上下级关系.xlsx"
        df2 = pd.read_excel(df2_path)
        print(f"表2读取成功，共{len(df2)}行数据")
        print(f"表2列名：{list(df2.columns)}")
        
        return df1, df2
    except Exception as e:
        print(f"读取文件时出错：{e}")
        return None, None

def extract_target_parts(df1):
    """提取表1中的目标件号"""
    # 假设件号在第一列或名为'件号'的列
    part_number_columns = ['件号', 'Part Number', 'PN', '零件号', '部件号', 'pnr']
    
    part_col = None
    for col in part_number_columns:
        if col in df1.columns:
            part_col = col
            break
    
    if part_col is None:
        part_col = df1.columns[0]
    
    print(f"表1使用列：{part_col}")
    
    # 提取件号集合
    target_parts = set(df1[part_col].dropna().astype(str).str.strip())
    print(f"表1中有效件号数量：{len(target_parts)}")
    
    return target_parts

def analyze_branches_for_parts(target_parts, df2):
    """为每个目标件号分析所属的枝"""
    print("\n开始分析每个件号的枝...")
    
    # 确保使用正确的列名
    required_cols = ['item_path', 'pnr', 'hierarchy_id', 'hierarchy_seq', 'be_cmm']
    missing_cols = [col for col in required_cols if col not in df2.columns]
    if missing_cols:
        print(f"警告：表2缺少列：{missing_cols}")
        print(f"可用列：{list(df2.columns)}")
        return None
    
    # 为每个目标件号找到所有相关的枝
    part_to_branches = {}
    
    for part in target_parts:
        # 查找该件号在表2中的所有记录
        part_records = df2[df2['pnr'].astype(str).str.strip() == part]
        
        if len(part_records) > 0:
            # 获取该件号所在的所有枝ID
            branch_ids = part_records['hierarchy_id'].unique()
            part_to_branches[part] = list(branch_ids)
            print(f"件号 {part} 找到 {len(branch_ids)} 个枝")
        else:
            part_to_branches[part] = []
            print(f"件号 {part} 未找到相关枝")
    
    return part_to_branches

def select_optimal_branches(target_parts, part_to_branches, df2):
    """为每个件号选择最优枝"""
    print("\n开始选择最优枝...")
    
    # 统计每个枝包含多少个目标件号
    branch_target_count = defaultdict(int)
    branch_to_parts = defaultdict(set)
    
    for part, branch_ids in part_to_branches.items():
        for branch_id in branch_ids:
            # 获取该枝的所有件号
            branch_records = df2[df2['hierarchy_id'] == branch_id]
            branch_parts = set(branch_records['pnr'].astype(str).str.strip())
            
            # 统计该枝包含多少个目标件号
            target_parts_in_branch = branch_parts.intersection(target_parts)
            branch_target_count[branch_id] = len(target_parts_in_branch)
            branch_to_parts[branch_id] = target_parts_in_branch
    
    print(f"总共分析了 {len(branch_target_count)} 个不同的枝")
    
    # 为每个件号选择最优枝
    part_to_selected_branch = {}
    used_branches = set()
    processed_parts = set()
    
    # 按枝包含的目标件号数量降序排序
    sorted_branches = sorted(branch_target_count.items(), key=lambda x: x[1], reverse=True)
    
    for branch_id, target_count in sorted_branches:
        if target_count == 0:
            continue
            
        # 获取该枝包含的目标件号
        parts_in_branch = branch_to_parts[branch_id]
        unprocessed_parts = parts_in_branch - processed_parts
        
        if unprocessed_parts:
            # 为这些件号分配该枝
            for part in unprocessed_parts:
                part_to_selected_branch[part] = branch_id
                processed_parts.add(part)
            
            used_branches.add(branch_id)
            print(f"枝 {branch_id} 被选中，包含 {len(unprocessed_parts)} 个目标件号：{unprocessed_parts}")
    
    # 处理没有找到枝的件号
    unmatched_parts = target_parts - processed_parts
    if unmatched_parts:
        print(f"\n警告：{len(unmatched_parts)} 个件号未找到合适的枝：{unmatched_parts}")
    
    print(f"\n总结：")
    print(f"- 成功匹配件号：{len(processed_parts)}")
    print(f"- 使用的枝数量：{len(used_branches)}")
    print(f"- 未匹配件号：{len(unmatched_parts)}")
    
    return part_to_selected_branch, used_branches

def create_output_excel(part_to_selected_branch, df2, target_parts, output_path="IPC枝分析结果.xlsx"):
    """创建输出Excel文件"""
    print("\n生成输出文件...")

    # 获取所有选中枝的完整数据
    selected_branches = set(part_to_selected_branch.values())

    # 提取所有选中枝的完整数据
    all_branch_data = []
    for branch_id in selected_branches:
        branch_records = df2[df2['hierarchy_id'] == branch_id].copy()

        # 标记哪些件号是目标件号
        branch_records['是否目标件号'] = branch_records['pnr'].astype(str).str.strip().isin(target_parts)

        # 添加到结果中
        for _, record in branch_records.iterrows():
            all_branch_data.append({
                "枝ID": record['hierarchy_id'],
                "项次路径": record['item_path'],
                "件号": record['pnr'],
                "层级序号": record['hierarchy_seq'],
                "是否CMM件": record['be_cmm'],
                "是否目标件号": "是" if record['是否目标件号'] else "否"
            })

    # 创建主要结果DataFrame并排序
    main_output_df = pd.DataFrame(all_branch_data)
    if len(main_output_df) > 0:
        main_output_df = main_output_df.sort_values(['枝ID', '层级序号'], ascending=[True, True])

    # 准备件号匹配摘要数据
    summary_data = []
    for part, branch_id in part_to_selected_branch.items():
        summary_data.append({
            "件号": part,
            "选定枝ID": branch_id,
            "状态": "已匹配"
        })

    # 添加未匹配的件号
    unmatched_parts = target_parts - set(part_to_selected_branch.keys())
    for part in unmatched_parts:
        summary_data.append({
            "件号": part,
            "选定枝ID": "未找到",
            "状态": "未匹配"
        })

    # 创建摘要DataFrame
    summary_df = pd.DataFrame(summary_data)
    
    # 统计每个枝的件号数量
    branch_stats = []
    used_branches = set(part_to_selected_branch.values())

    for branch_id in used_branches:
        parts_in_branch = [part for part, bid in part_to_selected_branch.items() if bid == branch_id]
        branch_records = df2[df2['hierarchy_id'] == branch_id]

        branch_stats.append({
            "枝ID": branch_id,
            "包含目标件号数": len(parts_in_branch),
            "目标件号列表": ", ".join(sorted(parts_in_branch)),
            "枝总件号数": len(branch_records),
            "枝中首个项次路径": branch_records['item_path'].iloc[0] if len(branch_records) > 0 else ""
        })

    branch_stats_df = pd.DataFrame(branch_stats)
    if len(branch_stats_df) > 0:
        branch_stats_df = branch_stats_df.sort_values('枝ID', ascending=True)

    # 保存到Excel
    with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
        # 主要结果：所有选中枝的完整数据
        main_output_df.to_excel(writer, sheet_name='选中枝完整数据', index=False)

        # 件号匹配摘要
        summary_df.to_excel(writer, sheet_name='件号匹配摘要', index=False)

        # 枝统计信息
        branch_stats_df.to_excel(writer, sheet_name='枝统计信息', index=False)

        # 添加总体统计
        total_stats_data = [
            ["总目标件号数", len(target_parts)],
            ["成功匹配件号数", len(part_to_selected_branch)],
            ["未匹配件号数", len(unmatched_parts)],
            ["使用的枝数量", len(used_branches)],
            ["匹配成功率", f"{len(part_to_selected_branch)/len(target_parts)*100:.1f}%"],
            ["选中枝总记录数", len(main_output_df)]
        ]
        total_stats_df = pd.DataFrame(total_stats_data, columns=["统计项", "数值"])
        total_stats_df.to_excel(writer, sheet_name='总体统计', index=False)

    print(f"结果已保存到：{output_path}")
    print(f"主要结果包含 {len(main_output_df)} 条记录，涵盖 {len(used_branches)} 个枝的完整数据")
    return main_output_df

def main():
    """主函数"""
    print("开始IPC枝分析...")
    
    # 1. 读取Excel文件
    df1, df2 = read_excel_files()
    if df1 is None or df2 is None:
        return
    
    # 2. 提取目标件号
    target_parts = extract_target_parts(df1)
    
    # 3. 分析每个件号的枝
    part_to_branches = analyze_branches_for_parts(target_parts, df2)
    if part_to_branches is None:
        return
    
    # 4. 选择最优枝
    part_to_selected_branch, used_branches = select_optimal_branches(target_parts, part_to_branches, df2)
    
    # 5. 创建输出文件
    result_df = create_output_excel(part_to_selected_branch, df2, target_parts)
    
    print("\n分析完成！")
    print(f"成功为 {len(part_to_selected_branch)} 个件号找到了最优枝")
    print("详细结果请查看生成的Excel文件")

if __name__ == "__main__":
    main()
