#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IPC结果增强工具
功能：为IPC枝分析结果添加互换件信息并进行格式化
"""

import pandas as pd
import numpy as np
from openpyxl import load_workbook
from openpyxl.styles import PatternFill
from collections import defaultdict

def read_source_files():
    """读取源文件"""
    try:
        # 读取IPC枝分析结果
        ipc_file = "IPC枝分析结果.xlsx"
        ipc_df = pd.read_excel(ipc_file, sheet_name='选中枝完整数据')
        print(f"IPC枝分析结果读取成功，共{len(ipc_df)}行数据")
        print(f"列名：{list(ipc_df.columns)}")
        
        # 读取所有件号选装件分析结果
        optional_file = "所有件号选装件分析结果.xlsx"
        optional_df = pd.read_excel(optional_file, sheet_name='所有件号分析结果')
        print(f"选装件分析结果读取成功，共{len(optional_df)}行数据")
        print(f"列名：{list(optional_df.columns)}")
        
        return ipc_df, optional_df
    except Exception as e:
        print(f"读取文件时出错：{e}")
        return None, None

def build_interchange_mapping(optional_df):
    """构建互换件映射关系"""
    print("\n构建互换件映射关系...")
    
    # 筛选出件号数量>2的组合（真正的互换件组）
    interchange_groups = optional_df[optional_df['件号数量'] > 2].copy()
    print(f"找到{len(interchange_groups)}个互换件组")
    
    # 构建件号到互换件组的映射
    part_to_interchange = {}
    
    for _, row in interchange_groups.iterrows():
        parts_str = row['件号组合']
        if pd.notna(parts_str):
            # 解析件号组合
            parts = [part.strip() for part in str(parts_str).split(',')]
            
            # 为每个件号建立映射
            for part in parts:
                if part:
                    # 互换件是除了自己之外的其他件号
                    other_parts = [p for p in parts if p != part]
                    if other_parts:
                        part_to_interchange[part] = ', '.join(other_parts)
    
    print(f"建立了{len(part_to_interchange)}个件号的互换件映射")
    return part_to_interchange

def add_interchange_column(ipc_df, part_to_interchange):
    """添加互换件列"""
    print("\n添加互换件信息...")
    
    # 添加互换件列
    ipc_df['互换件'] = ''
    
    # 只为"是否目标件号"为"是"的记录查找互换件
    target_mask = ipc_df['是否目标件号'] == '是'
    target_count = target_mask.sum()
    print(f"需要查找互换件的目标件号记录：{target_count}条")
    
    found_count = 0
    for idx, row in ipc_df[target_mask].iterrows():
        part_num = str(row['件号']).strip()
        if part_num in part_to_interchange:
            ipc_df.at[idx, '互换件'] = part_to_interchange[part_num]
            found_count += 1
    
    print(f"成功找到互换件的记录：{found_count}条")
    return ipc_df

def identify_highlight_groups(ipc_df):
    """识别需要高亮的组"""
    print("\n识别需要高亮的枝组...")

    # 按枝ID分组，找出包含目标件号且总记录数≥2的组
    highlight_groups = set()

    grouped = ipc_df.groupby('枝ID')
    for branch_id, group in grouped:
        # 检查这个组是否包含目标件号
        has_target = (group['是否目标件号'] == '是').any()
        # 检查组的总记录数是否≥2
        group_size = len(group)

        if has_target and group_size >= 2:
            highlight_groups.add(branch_id)
            print(f"枝 {branch_id}: 总记录数={group_size}, 包含目标件号, 将被高亮")

    print(f"需要高亮的枝组数量：{len(highlight_groups)}")
    return highlight_groups

def save_enhanced_result(ipc_df, highlight_groups, output_file="IPC枝分析结果_增强版.xlsx"):
    """保存增强后的结果并应用格式化"""
    print(f"\n保存增强结果到：{output_file}")
    
    # 重新排列列的顺序，将互换件列放在合适位置
    columns = list(ipc_df.columns)
    if '互换件' in columns:
        columns.remove('互换件')
        # 将互换件列放在"是否目标件号"之后
        target_idx = columns.index('是否目标件号') + 1
        columns.insert(target_idx, '互换件')
        ipc_df = ipc_df[columns]
    
    # 先保存到Excel
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        ipc_df.to_excel(writer, sheet_name='增强版枝完整数据', index=False)
        
        # 添加统计信息
        stats_data = [
            ["总记录数", len(ipc_df)],
            ["总枝数", ipc_df['枝ID'].nunique()],
            ["目标件号记录数", (ipc_df['是否目标件号'] == '是').sum()],
            ["有互换件的记录数", (ipc_df['互换件'] != '').sum()],
            ["需要高亮的枝数", len(highlight_groups)]
        ]
        stats_df = pd.DataFrame(stats_data, columns=["统计项", "数值"])
        stats_df.to_excel(writer, sheet_name='统计信息', index=False)
    
    # 应用格式化
    apply_formatting(output_file, highlight_groups)
    
    print("增强结果保存完成！")
    return output_file

def apply_formatting(file_path, highlight_groups):
    """应用格式化：为包含目标件号的枝组涂红色"""
    print("应用格式化...")
    
    try:
        # 加载工作簿
        wb = load_workbook(file_path)
        ws = wb['增强版枝完整数据']
        
        # 定义红色填充
        red_fill = PatternFill(start_color='FFCCCC', end_color='FFCCCC', fill_type='solid')
        
        # 读取数据以确定需要高亮的行
        data_rows = []
        for row in ws.iter_rows(min_row=2, values_only=True):  # 跳过标题行
            if row[0] is not None:  # 枝ID不为空
                data_rows.append(row)
        
        # 应用格式化
        highlight_count = 0
        for row_idx, row_data in enumerate(data_rows, start=2):  # Excel行号从2开始（跳过标题）
            branch_id = row_data[0]  # 枝ID在第一列
            
            if branch_id in highlight_groups:
                # 为整行应用红色背景
                for col_idx in range(1, len(row_data) + 1):
                    cell = ws.cell(row=row_idx, column=col_idx)
                    cell.fill = red_fill
                highlight_count += 1
        
        # 保存格式化后的文件
        wb.save(file_path)
        print(f"格式化完成，共高亮了{highlight_count}行数据")
        
    except Exception as e:
        print(f"应用格式化时出错：{e}")

def create_summary_report(ipc_df, highlight_groups):
    """创建汇总报告"""
    print("\n生成汇总报告...")

    # 基本统计
    total_records = len(ipc_df)
    total_branches = ipc_df['枝ID'].nunique()
    target_records = (ipc_df['是否目标件号'] == '是').sum()
    interchange_records = (ipc_df['互换件'] != '').sum()

    # 按枝统计
    branch_stats = []
    grouped = ipc_df.groupby('枝ID')

    single_record_branches = 0  # 单记录枝数量
    multi_record_branches = 0   # 多记录枝数量
    multi_with_target = 0       # 多记录且包含目标件号的枝数量

    for branch_id, group in grouped:
        target_count = (group['是否目标件号'] == '是').sum()
        interchange_count = (group['互换件'] != '').sum()
        is_highlighted = branch_id in highlight_groups
        group_size = len(group)

        if group_size == 1:
            single_record_branches += 1
        else:
            multi_record_branches += 1
            if target_count > 0:
                multi_with_target += 1

        branch_stats.append({
            '枝ID': branch_id,
            '总记录数': group_size,
            '目标件号数': target_count,
            '有互换件数': interchange_count,
            '是否高亮': '是' if is_highlighted else '否'
        })

    branch_stats_df = pd.DataFrame(branch_stats)

    print(f"汇总统计：")
    print(f"- 总记录数：{total_records}")
    print(f"- 总枝数：{total_branches}")
    print(f"- 单记录枝数：{single_record_branches}")
    print(f"- 多记录枝数：{multi_record_branches}")
    print(f"- 多记录且包含目标件号枝数：{multi_with_target}")
    print(f"- 目标件号记录数：{target_records}")
    print(f"- 有互换件记录数：{interchange_records}")
    print(f"- 高亮枝数：{len(highlight_groups)}")

    return branch_stats_df

def main():
    """主函数"""
    print("开始IPC结果增强处理...")
    
    # 1. 读取源文件
    ipc_df, optional_df = read_source_files()
    if ipc_df is None or optional_df is None:
        return
    
    # 2. 构建互换件映射
    part_to_interchange = build_interchange_mapping(optional_df)
    
    # 3. 添加互换件列
    enhanced_ipc_df = add_interchange_column(ipc_df.copy(), part_to_interchange)
    
    # 4. 识别需要高亮的组
    highlight_groups = identify_highlight_groups(enhanced_ipc_df)
    
    # 5. 保存增强结果
    output_file = save_enhanced_result(enhanced_ipc_df, highlight_groups)
    
    # 6. 生成汇总报告
    branch_stats_df = create_summary_report(enhanced_ipc_df, highlight_groups)
    
    print(f"\n处理完成！")
    print(f"增强版文件：{output_file}")
    print("主要改进：")
    print("1. 为目标件号添加了互换件信息")
    print("2. 包含目标件号的枝组已用红色高亮显示")
    print("3. 生成了详细的统计信息")

if __name__ == "__main__":
    main()
