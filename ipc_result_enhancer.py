#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IPC结果增强工具
功能：为IPC枝分析结果添加选装件信息并进行格式化
"""

import pandas as pd
import numpy as np
from openpyxl import load_workbook
from openpyxl.styles import PatternFill
from collections import defaultdict

def read_source_files():
    """读取源文件"""
    try:
        # 读取IPC枝分析结果
        ipc_file = "IPC枝分析结果.xlsx"
        ipc_df = pd.read_excel(ipc_file, sheet_name='选中枝完整数据')
        print(f"IPC枝分析结果读取成功，共{len(ipc_df)}行数据")
        print(f"列名：{list(ipc_df.columns)}")
        
        # 读取所有件号选装件分析结果
        optional_file = "所有件号选装件分析结果.xlsx"
        optional_df = pd.read_excel(optional_file, sheet_name='所有件号分析结果')
        print(f"选装件分析结果读取成功，共{len(optional_df)}行数据")
        print(f"列名：{list(optional_df.columns)}")
        
        return ipc_df, optional_df
    except Exception as e:
        print(f"读取文件时出错：{e}")
        return None, None

def build_interchange_mapping(optional_df):
    """构建选装件映射关系"""
    print("\n构建选装件映射关系...")
    
    # 筛选出件号数量>2的组合（真正的选装件组）
    interchange_groups = optional_df[optional_df['件号数量'] > 2].copy()
    print(f"找到{len(interchange_groups)}个选装件组")
    
    # 构建件号到选装件组的映射
    part_to_interchange = {}
    
    for _, row in interchange_groups.iterrows():
        parts_str = row['件号组合']
        if pd.notna(parts_str):
            # 解析件号组合
            parts = [part.strip() for part in str(parts_str).split(',')]
            
            # 为每个件号建立映射
            for part in parts:
                if part:
                    # 选装件是除了自己之外的其他件号
                    other_parts = [p for p in parts if p != part]
                    if other_parts:
                        part_to_interchange[part] = ', '.join(other_parts)
    
    print(f"建立了{len(part_to_interchange)}个件号的选装件映射")
    return part_to_interchange

def add_interchange_column(ipc_df, part_to_interchange):
    """添加选装件列"""
    print("\n添加选装件信息...")
    
    # 添加选装件列
    ipc_df['选装件'] = ''
    
    # 只为"是否目标件号"为"是"的记录查找选装件
    target_mask = ipc_df['是否目标件号'] == '是'
    target_count = target_mask.sum()
    print(f"需要查找选装件的目标件号记录：{target_count}条")
    
    found_count = 0
    for idx, row in ipc_df[target_mask].iterrows():
        part_num = str(row['件号']).strip()
        if part_num in part_to_interchange:
            ipc_df.at[idx, '选装件'] = part_to_interchange[part_num]
            found_count += 1
    
    print(f"成功找到选装件的记录：{found_count}条")
    return ipc_df

# 删除高亮识别函数，不再需要

def save_enhanced_result(ipc_df, output_file="IPC枝分析结果_增强版.xlsx"):
    """保存增强后的结果"""
    print(f"\n保存增强结果到：{output_file}")

    # 重新排列列的顺序，将选装件列放在合适位置
    columns = list(ipc_df.columns)
    if '选装件' in columns:
        columns.remove('选装件')
        # 将选装件列放在"是否目标件号"之后
        target_idx = columns.index('是否目标件号') + 1
        columns.insert(target_idx, '选装件')
        ipc_df = ipc_df[columns]

    # 保存到Excel
    ipc_df.to_excel(output_file, index=False)

    print(f"增强结果保存完成！")
    print(f"已添加选装件列，共{len(ipc_df)}条记录")
    return output_file

# 删除格式化函数，不再需要涂色功能

def create_summary_report(ipc_df):
    """创建汇总报告"""
    print("\n生成汇总报告...")

    # 基本统计
    total_records = len(ipc_df)
    total_branches = ipc_df['枝ID'].nunique()
    target_records = (ipc_df['是否目标件号'] == '是').sum()
    interchange_records = (ipc_df['选装件'] != '').sum()

    print(f"汇总统计：")
    print(f"- 总记录数：{total_records}")
    print(f"- 总枝数：{total_branches}")
    print(f"- 目标件号记录数：{target_records}")
    print(f"- 有选装件记录数：{interchange_records}")

    return None

def main():
    """主函数"""
    print("开始IPC结果增强处理...")

    # 1. 读取源文件
    ipc_df, optional_df = read_source_files()
    if ipc_df is None or optional_df is None:
        return

    # 2. 构建选装件映射
    part_to_interchange = build_interchange_mapping(optional_df)

    # 3. 添加选装件列
    enhanced_ipc_df = add_interchange_column(ipc_df.copy(), part_to_interchange)

    # 4. 保存增强结果
    output_file = save_enhanced_result(enhanced_ipc_df)

    # 5. 生成汇总报告
    create_summary_report(enhanced_ipc_df)

    print(f"\n处理完成！")
    print(f"增强版文件：{output_file}")
    print("主要改进：")
    print("1. 为目标件号添加了选装件信息")
    print("2. 保持了原有的数据结构和排序")

if __name__ == "__main__":
    main()
