#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
选装件分析工具
功能：分析780个件号中的互为选装件关系
"""

import pandas as pd
import numpy as np
from collections import defaultdict
import re

def read_excel_files():
    """读取两个Excel文件"""
    try:
        # 读取表1：780个件号清单
        df1_path = "data/780个需重新梳理树结构737ng器材清单20250822v1.xlsx"
        df1 = pd.read_excel(df1_path)
        print(f"表1读取成功，共{len(df1)}行数据")
        print(f"表1列名：{list(df1.columns)}")
        
        # 读取表2：拆机信息
        df2_path = "data/拆机信息.xlsx"
        df2 = pd.read_excel(df2_path)
        print(f"表2读取成功，共{len(df2)}行数据")
        print(f"表2列名：{list(df2.columns)}")
        
        return df1, df2
    except Exception as e:
        print(f"读取文件时出错：{e}")
        return None, None

def extract_part_numbers(df1, df2):
    """提取件号信息"""
    # 从表1提取件号（假设件号在第一列或名为'件号'的列）
    part_number_columns = ['件号', 'Part Number', 'PN', '零件号', '部件号']
    
    df1_part_col = None
    for col in part_number_columns:
        if col in df1.columns:
            df1_part_col = col
            break
    
    if df1_part_col is None:
        # 如果没找到，使用第一列
        df1_part_col = df1.columns[0]
    
    print(f"表1使用列：{df1_part_col}")
    
    # 从表2提取件号和选装件信息
    df2_part_col = None
    df2_optional_col = None
    
    for col in part_number_columns:
        if col in df2.columns:
            df2_part_col = col
            break
    
    optional_columns = ['选装件', '选装件列表', 'Optional Parts', '可选件', '替换件']
    for col in optional_columns:
        if col in df2.columns:
            df2_optional_col = col
            break
    
    if df2_part_col is None:
        df2_part_col = df2.columns[0]
    if df2_optional_col is None:
        df2_optional_col = df2.columns[1] if len(df2.columns) > 1 else df2.columns[0]
    
    print(f"表2使用列 - 件号：{df2_part_col}，选装件：{df2_optional_col}")
    
    # 提取表1的件号列表
    df1_parts = set(df1[df1_part_col].dropna().astype(str).str.strip())
    print(f"表1中有效件号数量：{len(df1_parts)}")
    
    return df1_parts, df2, df2_part_col, df2_optional_col

def parse_optional_parts(optional_str):
    """解析选装件字符串，支持逗号分割的多个件号"""
    if pd.isna(optional_str) or optional_str == '':
        return []
    
    # 转换为字符串并清理
    optional_str = str(optional_str).strip()
    
    # 使用逗号、分号、空格等分割符
    parts = re.split(r'[,，;；\s]+', optional_str)
    
    # 清理并过滤空字符串
    parts = [part.strip() for part in parts if part.strip()]
    
    return parts

def find_mutual_optional_parts(df1_parts, df2, df2_part_col, df2_optional_col):
    """查找互为选装件的关系"""
    # 建立件号到选装件的映射
    part_to_optionals = {}
    
    for _, row in df2.iterrows():
        part_num = str(row[df2_part_col]).strip()
        optional_parts = parse_optional_parts(row[df2_optional_col])
        
        if part_num in df1_parts and optional_parts:
            # 过滤出也在表1中的选装件
            valid_optionals = [opt for opt in optional_parts if opt in df1_parts]
            if valid_optionals:
                part_to_optionals[part_num] = valid_optionals
    
    print(f"找到{len(part_to_optionals)}个件号有选装件关系")
    
    # 查找互为选装件的关系
    mutual_groups = []
    processed_parts = set()
    
    for part, optionals in part_to_optionals.items():
        if part in processed_parts:
            continue
        
        # 当前组的件号集合
        current_group = {part}
        
        # 检查每个选装件是否也将当前件号作为选装件
        for optional in optionals:
            if optional in part_to_optionals:
                # 检查是否互为选装件
                if part in part_to_optionals[optional]:
                    current_group.add(optional)
        
        # 如果组中有多个件号，说明存在互为选装件关系
        if len(current_group) > 1:
            mutual_groups.append(current_group)
            processed_parts.update(current_group)
    
    print(f"找到{len(mutual_groups)}个互为选装件的组")
    
    return mutual_groups

def create_output_excel(mutual_groups, output_path="互为选装件分析结果.xlsx"):
    """创建输出Excel文件"""
    # 准备输出数据
    output_data = []
    
    for i, group in enumerate(mutual_groups, 1):
        # 将组中的件号用逗号连接
        parts_str = ", ".join(sorted(group))
        output_data.append({
            "组号": i,
            "互为选装件": parts_str,
            "件号数量": len(group)
        })
    
    # 创建DataFrame
    output_df = pd.DataFrame(output_data)
    
    # 保存到Excel
    with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
        output_df.to_excel(writer, sheet_name='互为选装件分析', index=False)
        
        # 添加统计信息
        summary_data = [
            ["总组数", len(mutual_groups)],
            ["涉及件号总数", sum(len(group) for group in mutual_groups)],
            ["平均每组件号数", sum(len(group) for group in mutual_groups) / len(mutual_groups) if mutual_groups else 0]
        ]
        summary_df = pd.DataFrame(summary_data, columns=["统计项", "数值"])
        summary_df.to_excel(writer, sheet_name='统计信息', index=False)
    
    print(f"结果已保存到：{output_path}")
    return output_df

def main():
    """主函数"""
    print("开始分析互为选装件关系...")
    
    # 1. 读取Excel文件
    df1, df2 = read_excel_files()
    if df1 is None or df2 is None:
        return
    
    # 2. 提取件号信息
    df1_parts, df2, df2_part_col, df2_optional_col = extract_part_numbers(df1, df2)
    
    # 3. 查找互为选装件关系
    mutual_groups = find_mutual_optional_parts(df1_parts, df2, df2_part_col, df2_optional_col)
    
    # 4. 创建输出文件
    if mutual_groups:
        result_df = create_output_excel(mutual_groups)
        print("\n分析完成！")
        print(f"共找到{len(mutual_groups)}个互为选装件组")
        
        # 显示前几个结果
        print("\n前5个结果预览：")
        print(result_df.head())
    else:
        print("未找到互为选装件关系")

if __name__ == "__main__":
    main()
