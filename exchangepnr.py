import pandas as pd
import pymysql
from openpyxl import load_workbook
import re
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import time
from queue import Queue


class DatabasePool:
    """数据库连接池"""

    def __init__(self, max_connections=10):
        self.max_connections = max_connections
        self.pool = Queue(maxsize=max_connections)
        self.lock = threading.Lock()

        # 初始化连接池
        for _ in range(max_connections):
            conn = self.create_connection()
            if conn:
                self.pool.put(conn)

    def create_connection(self):
        """创建数据库连接"""
        try:
            connection = pymysql.connect(
                host='*************',
                user='root',
                password='12345678',
                database='ipc',
                charset='utf8mb4',
                autocommit=True,
                connect_timeout=60,
                read_timeout=60,
                write_timeout=60
            )
            return connection
        except Exception as e:
            print(f"数据库连接失败: {e}")
            return None

    def get_connection(self):
        """从连接池获取连接"""
        try:
            return self.pool.get(timeout=30)
        except:
            # 如果池中没有连接，创建新连接
            return self.create_connection()

    def return_connection(self, conn):
        """归还连接到连接池"""
        if conn and conn.open:
            try:
                self.pool.put_nowait(conn)
            except:
                # 池满了，关闭连接
                conn.close()
        elif conn:
            conn.close()

    def close_all(self):
        """关闭所有连接"""
        while not self.pool.empty():
            try:
                conn = self.pool.get_nowait()
                if conn:
                    conn.close()
            except:
                break


def check_effrg_contains_028(effrg):
    """检查effrg字段是否包含028"""
    if not effrg or pd.isna(effrg):
        return True

    ranges = effrg.strip().split()
    for range_str in ranges:
        if len(range_str) == 6:
            try:
                start = int(range_str[:3])
                end = int(range_str[3:])
                if start <= 28 <= end:
                    return True
            except ValueError:
                continue
    return False


def get_interchange_parts_batch(db_pool, pnr_list):
    """批量查询互换件信息"""
    if not pnr_list:
        return {}

    connection = db_pool.get_connection()
    if not connection:
        return {}

    try:
        cursor = connection.cursor()
        results = {}

        # 批量查询第一步：根据pnr列表查询ipc_boeing_item表
        pnr_placeholders = ','.join(['%s'] * len(pnr_list))
        query1 = f"""
        SELECT pnr, uuid, effrg
        FROM ipc_boeing_item
        WHERE pnr IN ({pnr_placeholders})
          AND doc_id = 7376789
          AND data_status = 'Valid'
        """
        cursor.execute(query1, pnr_list)
        item_results = cursor.fetchall()

        # 按pnr分组并过滤适用于028的记录
        pnr_to_uuids = {}
        for pnr, uuid, effrg in item_results:
            if check_effrg_contains_028(effrg):
                if pnr not in pnr_to_uuids:
                    pnr_to_uuids[pnr] = []
                pnr_to_uuids[pnr].append(uuid)

        # 收集所有有效的uuid
        all_uuids = []
        for uuids in pnr_to_uuids.values():
            all_uuids.extend(uuids)

        if all_uuids:
            # 批量查询第二步：根据uuid列表查询ipc_boeing_item_ex表
            uuid_placeholders = ','.join(['%s'] * len(all_uuids))
            query2 = f"""
            SELECT item_uuid, ex_info
            FROM ipc_boeing_item_ex
            WHERE item_uuid IN ({uuid_placeholders})
              AND ex_type = 'OPTMFR'
              AND data_status = 'Valid'
              AND ex_info IS NOT NULL
              AND TRIM(ex_info) != ''
            """
            cursor.execute(query2, all_uuids)
            ex_results = cursor.fetchall()

            # 构建uuid到ex_info的映射
            uuid_to_ex_info = {}
            for item_uuid, ex_info in ex_results:
                if item_uuid not in uuid_to_ex_info:
                    uuid_to_ex_info[item_uuid] = []
                uuid_to_ex_info[item_uuid].append(ex_info.strip())

            # 为每个pnr收集互换件
            for pnr in pnr_list:
                interchange_parts = []
                if pnr in pnr_to_uuids:
                    for uuid in pnr_to_uuids[pnr]:
                        if uuid in uuid_to_ex_info:
                            interchange_parts.extend(uuid_to_ex_info[uuid])

                results[pnr] = ', '.join(interchange_parts) if interchange_parts else ''

        # 确保所有pnr都有结果
        for pnr in pnr_list:
            if pnr not in results:
                results[pnr] = ''

        cursor.close()
        return results

    except Exception as e:
        print(f"批量查询互换件失败: {e}")
        return {pnr: '' for pnr in pnr_list}
    finally:
        db_pool.return_connection(connection)


def process_pnr_batch(db_pool, pnr_batch_with_indices):
    """处理一批件号"""
    pnr_batch = [item[1] for item in pnr_batch_with_indices]
    valid_pnrs = [pnr for pnr in pnr_batch if pnr and not pd.isna(pnr) and str(pnr).strip()]

    if not valid_pnrs:
        return [(idx, '') for idx, _ in pnr_batch_with_indices]

    # 批量查询
    results = get_interchange_parts_batch(db_pool, [str(pnr).strip() for pnr in valid_pnrs])

    # 构建返回结果
    batch_results = []
    for idx, pnr in pnr_batch_with_indices:
        if pnr and not pd.isna(pnr) and str(pnr).strip():
            interchange_str = results.get(str(pnr).strip(), '')
        else:
            interchange_str = ''
        batch_results.append((idx, interchange_str))

    return batch_results


def process_excel_file():
    """处理Excel文件"""
    input_file = 'data/output_parallel_timeout.xlsx'
    output_file = 'data/output_parallel_timeout_with_interchange.xlsx'

    try:
        # 读取原始Excel文件
        print("正在读取Excel文件...")
        df = pd.read_excel(input_file)

        # 检查是否有件号列
        pnr_column = None
        for col in df.columns:
            if '件号' in str(col) or 'pnr' in str(col).lower():
                pnr_column = col
                break

        if pnr_column is None:
            print("未找到件号列，请检查Excel文件格式")
            return

        print(f"找到件号列: {pnr_column}")

        # 初始化数据库连接池
        db_pool = DatabasePool(max_connections=20)

        # 准备数据
        total_rows = len(df)
        print(f"共需处理 {total_rows} 条记录")

        # 将数据分批处理
        batch_size = 100  # 每批处理100个件号
        max_workers = 10  # 最大线程数

        # 创建带索引的件号列表
        pnr_with_indices = [(i, df.iloc[i][pnr_column]) for i in range(total_rows)]

        # 分批
        batches = []
        for i in range(0, len(pnr_with_indices), batch_size):
            batch = pnr_with_indices[i:i + batch_size]
            batches.append(batch)

        print(f"分为 {len(batches)} 批处理，每批 {batch_size} 条记录")

        # 多线程处理
        interchange_parts_dict = {}
        completed_batches = 0

        start_time = time.time()

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有批次任务
            future_to_batch = {
                executor.submit(process_pnr_batch, db_pool, batch): batch
                for batch in batches
            }

            # 收集结果
            for future in as_completed(future_to_batch):
                try:
                    batch_results = future.result()
                    for idx, interchange_str in batch_results:
                        interchange_parts_dict[idx] = interchange_str

                    completed_batches += 1
                    elapsed_time = time.time() - start_time
                    avg_time_per_batch = elapsed_time / completed_batches
                    remaining_batches = len(batches) - completed_batches
                    estimated_remaining_time = avg_time_per_batch * remaining_batches

                    print(f"已完成批次: {completed_batches}/{len(batches)} "
                          f"({completed_batches / len(batches) * 100:.1f}%) "
                          f"预计剩余时间: {estimated_remaining_time:.1f}秒")

                except Exception as e:
                    print(f"处理批次时出错: {e}")

        # 关闭数据库连接池
        db_pool.close_all()

        # 按索引顺序构建结果列表
        interchange_parts_list = []
        for i in range(total_rows):
            interchange_parts_list.append(interchange_parts_dict.get(i, ''))

        # 添加互换件列到DataFrame
        df['互换件'] = interchange_parts_list

        # 保存到新的Excel文件
        print("正在保存结果...")
        df.to_excel(output_file, index=False)

        total_time = time.time() - start_time
        found_count = sum(1 for x in interchange_parts_list if x)

        print(f"处理完成！结果已保存到: {output_file}")
        print(f"总处理时间: {total_time:.2f}秒")
        print(f"共处理 {total_rows} 条记录")
        print(f"找到互换件的记录数: {found_count}")
        print(f"平均处理速度: {total_rows / total_time:.2f} 条/秒")

    except Exception as e:
        print(f"处理Excel文件失败: {e}")


def main():
    """主函数"""
    print("开始处理互换件查询...")
    process_excel_file()
    print("处理完成！")


if __name__ == "__main__":
    main()