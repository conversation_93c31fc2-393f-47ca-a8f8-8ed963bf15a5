#!/usr/bin/env python
# -*- coding: utf-8 -*-
import pandas as pd
import requests
import time
from urllib.parse import quote
from concurrent.futures import ThreadPoolExecutor, as_completed, TimeoutError
from threading import Lock
import threading

# 全局锁，用于线程安全的打印
print_lock = Lock()


def thread_safe_print(*args, **kwargs):
    """线程安全的打印函数"""
    with print_lock:
        print(*args, **kwargs)


def get_pn_total_count(pn, doc_id=7376789, eff="028", timeout=300):
    """
    调用API获取件号在整个IPC中的安装数量
    """
    try:
        # URL编码件号，防止特殊字符问题
        encoded_pn = quote(str(pn))

        url = f"http://*************:11211/admin/ipc/countPnTotal"
        params = {
            'docId': doc_id,
            'pn': encoded_pn,
            'eff': eff
        }

        # 设置超时时间
        response = requests.get(url, params=params, timeout=timeout)
        response.raise_for_status()

        result = response.json()

        if result.get('success') and result.get('code') == 200:
            return pn, result.get('data', 0), 'success'
        else:
            thread_safe_print(f"API返回错误 - 件号: {pn}, 错误: {result.get('msg', '未知错误')}")
            return pn, 0, 'api_error'

    except requests.exceptions.Timeout:
        thread_safe_print(f"请求超时 - 件号: {pn} (超时时间: {timeout}秒)")
        return pn, 0, 'timeout'
    except requests.exceptions.RequestException as e:
        thread_safe_print(f"请求API失败 - 件号: {pn}, 错误: {e}")
        return pn, 0, 'request_error'
    except Exception as e:
        thread_safe_print(f"处理件号 {pn} 时发生错误: {e}")
        return pn, 0, 'unknown_error'


def process_pn_batch(pn_list, max_workers=15, request_timeout=300, task_timeout=300):
    """
    并行处理件号列表
    """
    pn_count_map = {}
    completed_count = 0
    total_count = len(pn_list)
    start_time = time.time()

    # 统计信息
    stats = {
        'success': 0,
        'timeout': 0,
        'api_error': 0,
        'request_error': 0,
        'task_timeout': 0,
        'unknown_error': 0
    }

    thread_safe_print(f"开始并行处理 {total_count} 个件号")
    thread_safe_print(f"使用 {max_workers} 个线程，请求超时: {request_timeout}秒，任务超时: {task_timeout}秒")
    thread_safe_print("注意：为了确保所有请求成功，使用了较长的超时时间，请耐心等待...")

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_pn = {
            executor.submit(get_pn_total_count, pn, timeout=request_timeout): pn
            for pn in pn_list
        }

        # 处理完成的任务 - 不设置总超时，让每个任务都有机会完成
        for future in as_completed(future_to_pn):
            try:
                # 为每个任务设置超时
                pn, count, status = future.result(timeout=task_timeout)
                pn_count_map[pn] = count
                completed_count += 1
                stats[status] += 1

                # 每处理完1个件号就打印进度（实时显示）
                elapsed_time = time.time() - start_time
                avg_time = elapsed_time / completed_count if completed_count > 0 else 0
                remaining_time = avg_time * (total_count - completed_count)
                success_rate = stats['success'] / completed_count * 100 if completed_count > 0 else 0

                thread_safe_print(
                    f"进度: {completed_count}/{total_count} ({completed_count / total_count * 100:.1f}%) "
                    f"成功率: {success_rate:.1f}% "
                    f"平均耗时: {avg_time:.1f}s "
                    f"预计剩余: {remaining_time / 60:.1f}分钟"
                )

            except TimeoutError:
                pn = future_to_pn[future]
                thread_safe_print(f"任务超时 - 件号: {pn} (任务超时: {task_timeout}秒)")
                pn_count_map[pn] = 0
                completed_count += 1
                stats['task_timeout'] += 1
            except Exception as e:
                pn = future_to_pn[future]
                thread_safe_print(f"处理件号 {pn} 时发生异常: {e}")
                pn_count_map[pn] = 0
                completed_count += 1
                stats['unknown_error'] += 1

    # 打印详细统计
    thread_safe_print(f"\n=== API调用统计 ===")
    thread_safe_print(f"总件号数: {total_count}")
    thread_safe_print(f"成功: {stats['success']} ({stats['success'] / total_count * 100:.1f}%)")
    thread_safe_print(f"请求超时: {stats['timeout']} ({stats['timeout'] / total_count * 100:.1f}%)")
    thread_safe_print(f"任务超时: {stats['task_timeout']} ({stats['task_timeout'] / total_count * 100:.1f}%)")
    thread_safe_print(f"API错误: {stats['api_error']} ({stats['api_error'] / total_count * 100:.1f}%)")
    thread_safe_print(f"网络错误: {stats['request_error']} ({stats['request_error'] / total_count * 100:.1f}%)")
    thread_safe_print(f"其他错误: {stats['unknown_error']} ({stats['unknown_error'] / total_count * 100:.1f}%)")

    return pn_count_map, stats


def process_excel_file(input_file, output_file, max_workers=15, request_timeout=300, task_timeout=300):
    """
    处理Excel文件，添加件号安装数量列（带超时控制）
    """
    try:
        # 读取Excel文件
        print(f"正在读取文件: {input_file}")
        df = pd.read_excel(input_file)

        print(f"文件读取成功，共 {len(df)} 行数据")
        print(f"列名: {list(df.columns)}")

        # 检查是否存在件号列
        pn_column = None
        for col in df.columns:
            if '件号' in str(col):
                pn_column = col
                break

        if pn_column is None:
            print("错误: 未找到包含'件号'的列")
            return False

        print(f"找到件号列: {pn_column}")

        # 添加新列
        df['该件号在整个IPC中的安装数量'] = 0

        # 获取唯一件号，减少API调用次数
        unique_pns = df[pn_column].dropna().unique()
        # 过滤空值
        unique_pns = [pn for pn in unique_pns if pd.notna(pn) and str(pn).strip() != '']

        print(f"发现 {len(unique_pns)} 个唯一件号")

        # 并行处理件号
        start_time = time.time()

        pn_count_map, stats = process_pn_batch(
            unique_pns,
            max_workers=max_workers,
            request_timeout=request_timeout,
            task_timeout=task_timeout
        )
        end_time = time.time()

        print(f"API调用完成，总耗时: {end_time - start_time:.2f} 秒")

        # 批量更新DataFrame
        print("正在更新Excel数据...")
        for index, row in df.iterrows():
            pn = row[pn_column]
            if pd.notna(pn) and pn in pn_count_map:
                df.at[index, '该件号在整个IPC中的安装数量'] = pn_count_map[pn]

        # 保存到新文件
        print(f"正在保存到文件: {output_file}")
        df.to_excel(output_file, index=False)

        print(f"处理完成！结果已保存到: {output_file}")

        # 打印最终统计信息
        print(f"\n=== 最终统计 ===")
        print(f"总件号数: {len(unique_pns)}")
        print(f"成功获取数量的件号: {stats['success']}")
        print(f"成功率: {stats['success'] / len(unique_pns) * 100:.1f}%")
        print(f"总处理时间: {end_time - start_time:.2f} 秒")
        print(f"平均每个件号耗时: {(end_time - start_time) / len(unique_pns):.3f} 秒")

        return True

    except Exception as e:
        print(f"处理Excel文件时发生错误: {e}")
        return False


def main():
    """
    主函数
    """
    # 配置文件路径
    input_file = "data/dwd_ipc_aircraft_item_hierarchy_detail2.xlsx"
    output_file = "data/output_parallel_timeout.xlsx"

    # 超时配置 - 保持长超时确保成功率
    max_workers = 15  # 并发线程数（适中，避免服务器压力过大）
    request_timeout = 300  # HTTP请求超时时间（5分钟）
    task_timeout = 300  # 单个任务超时时间（5分钟）

    print("=== Excel件号安装数量处理工具（高成功率版本）===")
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    print(f"API地址: http://*************:11211/admin/ipc/countPnTotal")
    print(f"固定参数: docId=7376789, eff=028")
    print(f"并发线程数: {max_workers}")
    print(f"请求超时: {request_timeout}秒（{request_timeout / 60:.1f}分钟）")
    print(f"任务超时: {task_timeout}秒（{task_timeout / 60:.1f}分钟）")
    print("注意：为确保高成功率，使用了较长超时时间，预计总耗时较长")
    print("=" * 60)

    # 处理文件
    success = process_excel_file(
        input_file,
        output_file,
        max_workers=max_workers,
        request_timeout=request_timeout,
        task_timeout=task_timeout
    )

    if success:
        print("\n✅ 处理成功完成！")
    else:
        print("\n❌ 处理失败，请检查错误信息")


if __name__ == "__main__":
    main()